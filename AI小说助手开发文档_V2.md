# AI小说助手开发文档 V2.0

## 文档信息
- **文档版本**: V2.0
- **创建日期**: 2025-01-29
- **最后更新**: 2025-01-29
- **项目名称**: AI小说助手
- **技术栈**: Electron + Vue 3 + TypeScript
- **开发模式**: 桌面应用，内置依赖

---

## 目录

1. [项目概述](#1-项目概述)
2. [技术架构](#2-技术架构)
3. [功能模块设计](#3-功能模块设计)
4. [界面布局设计](#4-界面布局设计)
5. [数据结构设计](#5-数据结构设计)
6. [API集成方案](#6-api集成方案)
7. [文件结构](#7-文件结构)
8. [开发路线图](#8-开发路线图)
9. [部署打包](#9-部署打包)
10. [测试方案](#10-测试方案)

---

## 1. 项目概述

### 1.1 项目简介
AI小说助手是一款基于Electron + Vue 3开发的桌面应用程序，旨在帮助作者使用AI技术创作多章节的长篇、中长篇、短篇网络小说。应用程序具备自动检索衔接上下文、伏笔管理等智能功能，支持多种AI模型接入。

### 1.2 核心特性
- **多AI模型支持**: GPT、Claude、Gemini、自定义OpenAI、ModelScope、Ollama、SiliconFlow
- **智能创作流程**: 大纲生成 → 章节编辑 → 内容创作 → 分析润色
- **上下文管理**: 自动检索衔接上下文、伏笔追踪
- **Glassmorphism UI**: 现代化毛玻璃设计风格，支持明亮/暗黑双主题
- **内置依赖**: 无需用户手动安装依赖，开箱即用
- **本地化存储**: 支持.ainovel格式的项目文件

### 1.3 目标用户
- 网络小说作者（番茄小说、飞卢、17K、起点、纵横、晋江、七猫等平台）
- 创意写作爱好者
- 内容创作者

---

## 2. 技术架构

### 2.1 技术栈选择

#### 2.1.1 前端技术栈
```
├── Electron 28.x          # 桌面应用框架
├── Vue 3.4.x              # 前端框架
├── TypeScript 5.x         # 类型安全
├── Vite 5.x               # 构建工具
├── Pinia 2.x              # 状态管理
├── Vue Router 4.x         # 路由管理
├── Element Plus 2.x       # UI组件库
├── Tailwind CSS 3.x       # 样式框架
└── Electron Builder      # 打包工具
```

#### 2.1.2 后端服务
```
├── Node.js 20.x           # 运行环境
├── Express 4.x            # API服务器
├── SQLite 3.x             # 本地数据库
├── Prisma 5.x             # ORM框架
├── Axios 1.x              # HTTP客户端
└── Electron Store 8.x     # 配置存储
```

#### 2.1.3 AI集成
```
├── OpenAI SDK             # GPT模型
├── Anthropic SDK          # Claude模型
├── Google AI SDK          # Gemini模型
├── Custom OpenAI API      # 自定义兼容API
├── ModelScope API         # 魔搭社区
├── Ollama API             # 本地模型
└── SiliconFlow API        # 硅基流动
```

### 2.2 架构设计

#### 2.2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Main Process                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Window Mgmt   │  │   File System   │  │   API Gateway   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   Electron Renderer Process                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    Vue 3 Application                    │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │   UI Layer  │  │ State Mgmt  │  │ Router Mgmt │     │ │
│  │  │ (Components)│  │   (Pinia)   │  │(Vue Router) │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │Service Layer│  │ Data Layer  │  │ Utils Layer │     │ │
│  │  │   (APIs)    │  │  (Models)   │  │ (Helpers)   │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      Local Storage                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   SQLite DB     │  │   File System   │  │   Config Store  │ │
│  │   (Projects)    │  │   (.ainovel)    │  │   (Settings)    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 2.2.2 数据流架构
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    UI层     │───▶│   状态层    │───▶│   服务层    │
│ Components  │    │   Pinia     │    │   APIs      │
└─────────────┘    └─────────────┘    └─────────────┘
       ▲                   ▲                   │
       │                   │                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   响应更新   │◀───│   状态更新   │◀───│  AI API调用  │
│             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
```

---

## 3. 功能模块设计

### 3.1 核心功能模块

#### 3.1.1 大纲生成模块
**功能描述**: 基于用户输入的基本信息，使用AI生成小说大纲

**核心功能**:
- 模型选择 (GPT/Claude/Gemini/自定义/ModelScope/Ollama/SiliconFlow)
- 提示词模板管理 (内置模板 + 自定义模板)
- 基本信息配置 (标题/类型/主题/风格/章节数/字数)
- 生成范围设置 (起始章-结束章)
- 大纲生成与清空操作

**数据结构**:
```typescript
interface OutlineConfig {
  title: string;           // 小说标题
  genre: string;          // 小说类型
  theme: string;          // 小说主题
  style: string;          // 小说风格
  chapterCount: number;   // 章节数 (1-9999)
  wordsPerChapter: number; // 每章字数 (300-9999)
  startChapter: number;   // 起始章
  endChapter: number;     // 结束章
  characterCounts: {      // 人物数量
    protagonist: number;  // 主角数量
    important: number;    // 重要角色数量
    supporting: number;   // 配角数量
    extras: number;       // 龙套数量
  };
}

interface PromptTemplate {
  id: string;
  name: string;           // 模板名称
  description: string;    // 模板描述
  category: string;       // 模板分类
  content: string;        // 模板内容
  variables: string[];    // 变量列表
  isBuiltIn: boolean;     // 是否内置
  createdAt: Date;
  updatedAt: Date;
}
```

#### 3.1.2 大纲编辑模块
**功能描述**: 对生成的大纲进行编辑和完善

**核心功能**:
- 小说标题编辑 (支持AI生成)
- 中心思想编辑 (支持AI生成)
- 故事梗概编辑 (支持AI生成)
- 世界观设定编辑 (支持AI生成)
- 保存修改功能

**数据结构**:
```typescript
interface NovelOutline {
  id: string;
  title: string;          // 小说标题
  coreTheme: string;      // 中心思想
  synopsis: string;       // 故事梗概
  worldSetting: string;   // 世界观设定
  chapters: Chapter[];    // 章节列表
  characters: Character[]; // 人物列表
  createdAt: Date;
  updatedAt: Date;
}
```

#### 3.1.3 章节编辑模块
**功能描述**: 管理章节结构和章节详细信息

**核心功能**:
- 章节列表管理 (添加/编辑/删除章节)
- 章节标题编辑 (支持AI生成)
- 章节摘要编辑 (支持角色选择和AI生成)
- 保存修改功能

**数据结构**:
```typescript
interface Chapter {
  id: string;
  chapterNumber: number;  // 章节号
  title: string;          // 章节标题
  summary: string;        // 章节摘要
  content: string;        // 章节内容
  wordCount: number;      // 字数统计
  characters: string[];   // 涉及角色
  plotPoints: string[];   // 剧情要点
  status: 'draft' | 'completed' | 'reviewed'; // 状态
  createdAt: Date;
  updatedAt: Date;
}
```

#### 3.1.4 章节生成模块
**功能描述**: 基于大纲和上下文生成章节内容

**核心功能**:
- 章节选择器
- 章节信息展示
- 内容生成 (支持角色选择、目标字数设置)
- AI辅助编辑 (选定文本润色)
- 保存章节功能

**数据结构**:
```typescript
interface ChapterGenerationConfig {
  chapterId: string;
  targetWordCount?: number; // 目标字数
  selectedCharacters: string[]; // 选择的角色
  contextChapters: string[]; // 上下文章节
  generationMode: 'full' | 'continue' | 'rewrite'; // 生成模式
  customPrompt?: string; // 自定义提示词
}

interface GenerationResult {
  content: string;
  wordCount: number;
  generatedAt: Date;
  model: string;
  confidence: number;
}
```

#### 3.1.5 人物编辑模块
**功能描述**: 管理小说中的人物角色

**核心功能**:
- 角色列表管理 (添加/编辑/删除角色)
- AI生成角色功能
- 角色详情编辑
- 模型选择支持

**数据结构**:
```typescript
interface Character {
  id: string;
  name: string;           // 角色姓名
  role: 'protagonist' | 'important' | 'supporting' | 'extra'; // 角色类型
  age?: number;           // 年龄
  gender: string;         // 性别
  appearance: string;     // 外貌描述
  personality: string;    // 性格特点
  background: string;     // 背景故事
  abilities: string[];    // 能力特长
  relationships: Relationship[]; // 人物关系
  plotRelevance: string;  // 剧情相关性
  firstAppearance?: number; // 首次出现章节
  tags: string[];         // 标签
  createdAt: Date;
  updatedAt: Date;
}

interface Relationship {
  id: string;
  characterId: string;    // 关联角色ID
  relationshipType: string; // 关系类型
  description: string;    // 关系描述
  strength: number;       // 关系强度 (1-10)
}
```

#### 3.1.6 人物关系图模块
**功能描述**: 可视化展示人物关系网络

**核心功能**:
- 关系图可视化展示
- 添加/更新/删除关系
- 关系类型管理
- 保存关系数据

**数据结构**:
```typescript
interface RelationshipGraph {
  nodes: RelationshipNode[];
  edges: RelationshipEdge[];
}

interface RelationshipNode {
  id: string;
  characterId: string;
  name: string;
  role: string;
  x: number;
  y: number;
}

interface RelationshipEdge {
  id: string;
  source: string;
  target: string;
  relationshipType: string;
  strength: number;
  description: string;
}
```

#### 3.1.7 章节分析模块
**功能描述**: 分析章节内容并提供改进建议

**核心功能**:
- 章节选择器
- 多维度分析 (核心剧情/故事梗概/优缺点/角色标注/物品标注/改进建议)
- 章节改进功能
- AI模型支持

**数据结构**:
```typescript
interface ChapterAnalysis {
  id: string;
  chapterId: string;
  analysisType: AnalysisType[];
  results: AnalysisResult[];
  suggestions: ImprovementSuggestion[];
  analyzedAt: Date;
  model: string;
}

type AnalysisType =
  | 'plot_analysis'      // 核心剧情分析
  | 'synopsis_extraction' // 故事梗概提取
  | 'pros_cons_analysis' // 优缺点分析
  | 'character_annotation' // 角色标注
  | 'item_annotation'    // 物品标注
  | 'improvement_suggestions'; // 改进建议

interface AnalysisResult {
  type: AnalysisType;
  content: string;
  confidence: number;
  highlights: TextHighlight[];
}

interface ImprovementSuggestion {
  category: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
}
```

#### 3.1.8 统计信息模块
**功能描述**: 实时统计小说创作进度和数据

**核心功能**:
- 概览统计 (标题/章节数/总字数/平均字数/完成度)
- 章节统计 (章节号/标题/字数)
- 实时更新
- 刷新统计功能

**数据结构**:
```typescript
interface NovelStatistics {
  overview: {
    title: string;
    totalChapters: number;
    totalWords: number;
    averageWordsPerChapter: number;
    completedChapters: number;
    completionRate: number; // 完成度百分比
  };
  chapterStats: ChapterStat[];
  lastUpdated: Date;
}

interface ChapterStat {
  chapterNumber: number;
  title: string;
  wordCount: number;
  status: string;
  lastModified: Date;
}
```

#### 3.1.9 AI聊天模块
**功能描述**: 与AI模型进行对话交流，验证模型可用性

**核心功能**:
- AI模型对话界面
- 模型可用性验证
- 写作相关问答
- 聊天记录管理

**数据结构**:
```typescript
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  model: string;
  tokens?: number;
}

interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  model: string;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 3.1.10 提示词库模块
**功能描述**: 管理内置和自定义提示词模板

**核心功能**:
- 内置提示词展示
- 自定义提示词管理 (新建/编辑/删除)
- 提示词分类管理
- 提示词搜索和筛选

**内置提示词分类**:
- 大纲相关、细纲、章节、世界观
- 剧情线、续写、扩写、润色、改写
- 优化建议、金手指生成、黄金开篇
- 写作风格、写作要求、人设生成
- 审稿、仿写、短篇等

**数据结构**:
```typescript
interface PromptLibrary {
  categories: PromptCategory[];
  templates: PromptTemplate[];
}

interface PromptCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  order: number;
}

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  content: string;
  variables: PromptVariable[];
  isBuiltIn: boolean;
  usage: number; // 使用次数
  rating: number; // 用户评分
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface PromptVariable {
  name: string;
  type: 'text' | 'number' | 'select' | 'textarea';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[]; // for select type
}
```

#### 3.1.11 上下文管理模块
**功能描述**: 管理小说创作的上下文信息和伏笔追踪

**核心功能**:
- 上下文自动检索
- 伏笔管理和追踪
- 情节连贯性检查
- 角色一致性验证

**数据结构**:
```typescript
interface ContextManager {
  plotThreads: PlotThread[];
  foreshadowing: Foreshadowing[];
  characterArcs: CharacterArc[];
  worldElements: WorldElement[];
}

interface PlotThread {
  id: string;
  name: string;
  description: string;
  startChapter: number;
  endChapter?: number;
  status: 'active' | 'resolved' | 'abandoned';
  relatedChapters: number[];
  importance: 'major' | 'minor' | 'subplot';
}

interface Foreshadowing {
  id: string;
  content: string;
  chapterIntroduced: number;
  chapterResolved?: number;
  type: 'character' | 'plot' | 'world' | 'mystery';
  status: 'planted' | 'developing' | 'resolved';
  relatedElements: string[];
}

interface CharacterArc {
  characterId: string;
  startChapter: number;
  endChapter?: number;
  arcType: 'growth' | 'fall' | 'redemption' | 'discovery';
  keyMoments: ArcMoment[];
  status: 'planning' | 'active' | 'completed';
}

interface ArcMoment {
  chapterNumber: number;
  description: string;
  significance: 'setup' | 'development' | 'climax' | 'resolution';
}
```

#### 3.1.12 向量库检索模块
**功能描述**: 基于向量相似度的内容检索系统

**核心功能**:
- 文本向量化存储
- 相似内容检索
- 嵌入模型配置
- 检索结果排序

**数据结构**:
```typescript
interface VectorDatabase {
  documents: VectorDocument[];
  embeddings: EmbeddingConfig;
  indices: VectorIndex[];
}

interface VectorDocument {
  id: string;
  content: string;
  type: 'chapter' | 'character' | 'outline' | 'note';
  sourceId: string; // 关联的章节/角色等ID
  vector: number[];
  metadata: Record<string, any>;
  createdAt: Date;
}

interface EmbeddingConfig {
  model: string; // 嵌入模型名称
  apiKey: string;
  apiUrl: string;
  dimensions: number;
  maxTokens: number;
}

interface VectorIndex {
  id: string;
  name: string;
  documentIds: string[];
  indexType: 'flat' | 'hnsw' | 'ivf';
  createdAt: Date;
}

interface SearchResult {
  documentId: string;
  content: string;
  similarity: number;
  metadata: Record<string, any>;
}
```

#### 3.1.13 设置模块
**功能描述**: 应用程序配置和API设置管理

**核心功能**:
- API密钥配置 (OpenAI/Claude/Gemini/ModelScope/SiliconFlow/自定义)
- 模型选择和配置
- 自定义模型管理
- API连接测试
- 设置保存和重置

**数据结构**:
```typescript
interface AppSettings {
  apiConfigs: APIConfig[];
  uiSettings: UISettings;
  editorSettings: EditorSettings;
  generalSettings: GeneralSettings;
}

interface APIConfig {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'modelscope' | 'ollama' | 'siliconflow' | 'custom';
  apiKey: string;
  apiUrl?: string;
  model: string;
  isActive: boolean;
  testStatus: 'untested' | 'success' | 'failed';
  lastTested?: Date;
  customHeaders?: Record<string, string>;
}

interface UISettings {
  theme: 'light' | 'dark';
  language: string;
  fontSize: number;
  windowSize: {
    width: number;
    height: number;
  };
  windowPosition: {
    x: number;
    y: number;
  };
}

interface EditorSettings {
  autoSave: boolean;
  autoSaveInterval: number; // 秒
  wordWrap: boolean;
  showWordCount: boolean;
  spellCheck: boolean;
  aiAssistanceLevel: 'minimal' | 'moderate' | 'aggressive';
}

interface GeneralSettings {
  defaultWordsPerChapter: number;
  maxChapters: number;
  backupEnabled: boolean;
  backupInterval: number; // 分钟
  logLevel: 'error' | 'warn' | 'info' | 'debug';
}
```

---

## 4. 界面布局设计

### 4.1 整体布局架构

#### 4.1.1 主界面布局 (首页仪表盘)
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏 (高度: 60px)                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   新建项目      │  │   打开项目      │  │   保存项目      │  │   导出项目      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航菜单 (宽度: 280px)  │                主内容区域                          │
│                            │                                                    │
│ ┌─────────────────────────┐ │ ┌─────────────────────────────────────────────────┐ │
│ │ 📊 仪表盘               │ │ │                                                 │ │
│ │ 📝 大纲生成             │ │ │              欢迎使用AI小说助手                  │ │
│ │ ✏️  大纲编辑             │ │ │                                                 │ │
│ │ 📑 章节编辑             │ │ │         ┌─────────────────────────────┐         │ │
│ │ ✍️  章节生成             │ │ │         │                             │         │ │
│ │ 🔍 章节分析             │ │ │         │        快速开始面板          │         │ │
│ │ 👥 人物编辑             │ │ │         │                             │         │ │
│ │ 🕸️  人物关系图           │ │ │         │  ┌─────────┐ ┌─────────┐    │         │ │
│ │ 📈 统计信息             │ │ │         │  │新建项目 │ │打开项目 │    │         │ │
│ │ 💬 AI聊天               │ │ │         │  └─────────┘ └─────────┘    │         │ │
│ │ 📚 提示词库             │ │ │         │                             │         │ │
│ │ 🧠 上下文管理           │ │ │         └─────────────────────────────┘         │ │
│ │ 🔎 向量库检索           │ │ │                                                 │ │
│ │ ⚙️  设置                │ │ │              最近项目列表                       │ │
│ └─────────────────────────┘ │ │                                                 │ │
│                            │ └─────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏 (高度: 30px)                              │
│ 当前项目: 未选择  │  AI模型: 未配置  │  字数统计: 0  │  最后保存: --  │  版本: v2.0 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.2 大纲生成界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                        大纲生成                                      │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │ 功能区域 (40%)                    │ 生成结果区域 (60%)                │
│         │                                  │                                  │
│         │ ┌─────────────────────────────────┐ │ ┌─────────────────────────────────┐ │
│         │ │          模型选择               │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │         生成的大纲内容           │ │
│         │ │ │ GPT-4 ▼                     │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        提示词模板               │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 标准大纲提示词 ▼             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        基本信息                 │ │ │                                 │ │
│         │ │ 小说标题: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │ 小说类型: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 玄幻修仙 ▼          │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │ 小说主题: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 成长冒险 ▼          │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │ 小说风格: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 轻松幽默 ▼          │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        章节设置                 │ │ │                                 │ │
│         │ │ 章节数: ┌─────┐ (1-9999)        │ │ │                                 │ │
│         │ │        │  10 │                 │ │ │                                 │ │
│         │ │        └─────┘                 │ │ │                                 │ │
│         │ │ 每章字数: ┌─────┐ (300-9999)    │ │ │                                 │ │
│         │ │          │3500 │               │ │ │                                 │ │
│         │ │          └─────┘               │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        人物设置                 │ │ │                                 │ │
│         │ │ 主角数量: ┌───┐                 │ │ │                                 │ │
│         │ │          │ 1 │                 │ │ │                                 │ │
│         │ │          └───┘                 │ │ │                                 │ │
│         │ │ 重要角色: ┌───┐                 │ │ │                                 │ │
│         │ │          │ 3 │                 │ │ │                                 │ │
│         │ │          └───┘                 │ │ │                                 │ │
│         │ │ 配角数量: ┌───┐                 │ │ │                                 │ │
│         │ │          │ 5 │                 │ │ │                                 │ │
│         │ │          └───┘                 │ │ │                                 │ │
│         │ │ 龙套数量: ┌───┐                 │ │ │                                 │ │
│         │ │          │ 10│                 │ │ │                                 │ │
│         │ │          └───┘                 │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        生成范围                 │ │ │                                 │ │
│         │ │ 起始章: ┌───┐ 结束章: ┌───┐     │ │ │                                 │ │
│         │ │        │ 1 │        │10 │     │ │ │                                 │ │
│         │ │        └───┘        └───┘     │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────┐ ┌─────────────┐ │ │ │                                 │ │
│         │ │ │  生成大纲   │ │  清空大纲   │ │ │ │                                 │ │
│         │ │ └─────────────┘ └─────────────┘ │ │ │                                 │ │
│         │ └─────────────────────────────────┘ │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.3 大纲编辑界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                        大纲编辑                                      │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │ 编辑功能区 (40%)                  │ 编辑内容区 (60%)                  │
│         │                                  │                                  │
│         │ ┌─────────────────────────────────┐ │ ┌─────────────────────────────────┐ │
│         │ │        基本信息编辑             │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 小说标题:                       │ │ │         当前大纲内容             │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │      AI生成标题             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 中心思想:                       │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │    AI生成中心思想           │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 故事梗概:                       │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │    AI生成故事梗概           │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 世界观设定:                     │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │   AI生成世界观设定          │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │        保存修改             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ └─────────────────────────────────┘ │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.4 章节编辑界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                        章节编辑                                      │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │ 章节管理区 (40%)                  │ 章节内容区 (60%)                  │
│         │                                  │                                  │
│         │ ┌─────────────────────────────────┐ │ ┌─────────────────────────────────┐ │
│         │ │        章节列表                 │ │ │                                 │ │
│         │ │                                 │ │ │        选中章节详情              │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │      添加章节               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 第1章: 初入修仙界           │ │ │ │                                 │ │
│         │ │ │ [编辑] [删除]               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 第2章: 奇遇仙缘             │ │ │ │                                 │ │
│         │ │ │ [编辑] [删除]               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 第3章: 修炼之路             │ │ │ │                                 │ │
│         │ │ │ [编辑] [删除]               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        章节详情编辑             │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 章节标题:                       │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │     AI生成标题              │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 章节摘要:                       │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ │                             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 选择角色: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 主角张三 ▼          │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │    AI生成章节摘要           │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │        保存修改             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ └─────────────────────────────────┘ │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.5 章节生成界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                        章节生成                                      │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │ 生成配置区 (40%)                  │ 生成内容区 (60%)                  │
│         │                                  │                                  │
│         │ ┌─────────────────────────────────┐ │ ┌─────────────────────────────────┐ │
│         │ │        章节选择                 │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 选择章节: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 第1章: 初入修仙界 ▼ │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        章节信息                 │ │ │                                 │ │
│         │ │ 章节标题: 初入修仙界             │ │ │                                 │ │
│         │ │ 当前字数: 0                     │ │ │                                 │ │
│         │ │ 目标字数: 3500                  │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        生成设置                 │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 选择角色: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 主角张三 ▼          │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 目标字数: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 3500               │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 生成模式: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 全新生成 ▼          │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │      AI生成章节内容         │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        AI辅助编辑               │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │     选定文本润色            │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │     续写内容                │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │     重写段落                │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │        保存章节             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ └─────────────────────────────────┘ │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.6 章节分析界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                        章节分析                                      │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │ 分析配置区 (40%)                  │ 分析结果区 (60%)                  │
│         │                                  │                                  │
│         │ ┌─────────────────────────────────┐ │ ┌─────────────────────────────────┐ │
│         │ │        章节选择                 │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 选择章节: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 第1章: 初入修仙界 ▼ │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 分析章节数: 第1章                │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        分析选项                 │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ☑ 核心剧情分析                  │ │ │                                 │ │
│         │ │ ☑ 故事梗概提取                  │ │ │                                 │ │
│         │ │ ☑ 优缺点分析                    │ │ │                                 │ │
│         │ │ ☑ 角色标注                      │ │ │                                 │ │
│         │ │ ☑ 物品标注                      │ │ │                                 │ │
│         │ │ ☑ 改进建议                      │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │        分析章节             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        分析结果                 │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 核心剧情分析:                   │ │ │                                 │ │
│         │ │ [显示分析结果]                  │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 故事梗概提取:                   │ │ │                                 │ │
│         │ │ [显示提取结果]                  │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 优缺点分析:                     │ │ │                                 │ │
│         │ │ [显示分析结果]                  │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 角色标注:                       │ │ │                                 │ │
│         │ │ [显示角色信息]                  │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 物品标注:                       │ │ │                                 │ │
│         │ │ [显示物品信息]                  │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 改进建议:                       │ │ │                                 │ │
│         │ │ [显示改进建议]                  │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │        章节改进             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ └─────────────────────────────────┘ │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.7 人物编辑界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                        人物编辑                                      │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │ 角色管理区 (40%)                  │ 角色详情区 (60%)                  │
│         │                                  │                                  │
│         │ ┌─────────────────────────────────┐ │ ┌─────────────────────────────────┐ │
│         │ │        角色列表                 │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │      添加角色               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │    使用AI生成角色           │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 模型选择: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ GPT-4 ▼             │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 👤 张三 (主角)              │ │ │ │                                 │ │
│         │ │ │ [编辑] [删除]               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 👥 李四 (重要角色)          │ │ │ │                                 │ │
│         │ │ │ [编辑] [删除]               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 👤 王五 (配角)              │ │ │ │                                 │ │
│         │ │ │ [编辑] [删除]               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        角色详情编辑             │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 角色姓名: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 角色类型: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │ 主角 ▼              │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 年龄: ┌─────┐ 性别: ┌─────────┐ │ │ │                                 │ │
│         │ │      │     │      │ 男 ▼    │ │ │ │                                 │ │
│         │ │      └─────┘      └─────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 外貌描述: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 性格特点: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 背景故事: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │        保存角色             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ └─────────────────────────────────┘ │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.8 人物关系图界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                       人物关系图                                     │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │ 关系管理区 (40%)                  │ 关系图展示区 (60%)                │
│         │                                  │                                  │
│         │ ┌─────────────────────────────────┐ │ ┌─────────────────────────────────┐ │
│         │ │        关系管理                 │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 角色1: ┌────────────────────────┐ │ │ │                                 │ │
│         │ │       │ 张三 ▼                 │ │ │ │                                 │ │
│         │ │       └────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 角色2: ┌────────────────────────┐ │ │ │                                 │ │
│         │ │       │ 李四 ▼                 │ │ │ │                                 │ │
│         │ │       └────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 关系: ┌─────────────────────────┐ │ │ │                                 │ │
│         │ │      │ 朋友 ▼                  │ │ │ │                                 │ │
│         │ │      └─────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ 关系描述: ┌─────────────────────┐ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          │                     │ │ │ │                                 │ │
│         │ │          └─────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │     添加/更新关系           │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │        现有关系列表             │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 张三 ←→ 李四 (朋友)         │ │ │ │                                 │ │
│         │ │ │ [编辑] [删除]               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │ 张三 ←→ 王五 (师徒)         │ │ │ │                                 │ │
│         │ │ │ [编辑] [删除]               │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ │                                 │ │ │                                 │ │
│         │ │ ┌─────────────────────────────┐ │ │ │                                 │ │
│         │ │ │        保存关系             │ │ │ │                                 │ │
│         │ │ └─────────────────────────────┘ │ │ │                                 │ │
│         │ └─────────────────────────────────┘ │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.9 统计信息界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                        统计信息                                      │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │                            统计信息展示区                            │
│         │                                                                      │
│         │ ┌─────────────────────────────────────────────────────────────────┐   │
│         │ │                        概览统计                                 │   │
│         │ │                                                                 │   │
│         │ │ 小说标题: 修仙传奇                                               │   │
│         │ │ 章节数: 10章                                                    │   │
│         │ │ 总字数: 35,000字                                                │   │
│         │ │ 平均每章字数: 3,500字                                           │   │
│         │ │ 已完成章节: 8章                                                 │   │
│         │ │ 完成度: 80%                                                     │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │                    进度条                                   │ │   │
│         │ │ │ ████████████████████████████████████████████████████████████ │ │   │
│         │ │ │ 80%                                                         │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ └─────────────────────────────────────────────────────────────────┘   │
│         │                                                                      │
│         │ ┌─────────────────────────────────────────────────────────────────┐   │
│         │ │                        章节统计                                 │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │        刷新统计                                             │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ │                                                                 │   │
│         │ │ ┌─────┬─────────────────────┬─────────┬─────────┬─────────────┐ │   │
│         │ │ │章节号│      章节标题       │  字数   │  状态   │  最后修改   │ │   │
│         │ │ ├─────┼─────────────────────┼─────────┼─────────┼─────────────┤ │   │
│         │ │ │  1  │    初入修仙界       │  3,500  │ 已完成  │ 2025-01-29  │ │   │
│         │ │ │  2  │    奇遇仙缘         │  3,200  │ 已完成  │ 2025-01-28  │ │   │
│         │ │ │  3  │    修炼之路         │  3,800  │ 已完成  │ 2025-01-27  │ │   │
│         │ │ │  4  │    突破境界         │  3,600  │ 已完成  │ 2025-01-26  │ │   │
│         │ │ │  5  │    历练江湖         │  3,400  │ 已完成  │ 2025-01-25  │ │   │
│         │ │ │  6  │    遇见红颜         │  3,700  │ 已完成  │ 2025-01-24  │ │   │
│         │ │ │  7  │    门派大比         │  3,900  │ 已完成  │ 2025-01-23  │ │   │
│         │ │ │  8  │    秘境探险         │  3,300  │ 已完成  │ 2025-01-22  │ │   │
│         │ │ │  9  │    强敌来袭         │    0    │ 草稿    │     --      │ │   │
│         │ │ │ 10  │    最终决战         │    0    │ 草稿    │     --      │ │   │
│         │ │ └─────┴─────────────────────┴─────────┴─────────┴─────────────┘ │   │
│         │ └─────────────────────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.10 AI聊天界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                        AI聊天                                       │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │                            AI聊天界面                               │
│         │                                                                      │
│         │ ┌─────────────────────────────────────────────────────────────────┐   │
│         │ │                        聊天配置                                 │   │
│         │ │                                                                 │   │
│         │ │ 选择模型: ┌─────────────────────┐ ┌─────────────────────────────┐ │   │
│         │ │          │ GPT-4 ▼             │ │        测试连接             │ │   │
│         │ │          └─────────────────────┘ └─────────────────────────────┘ │   │
│         │ └─────────────────────────────────────────────────────────────────┘   │
│         │                                                                      │
│         │ ┌─────────────────────────────────────────────────────────────────┐   │
│         │ │                        聊天记录                                 │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │ 用户: 你好，我想写一部修仙小说，有什么建议吗？               │ │   │
│         │ │ │ 时间: 2025-01-29 10:30                                      │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │ AI: 写修仙小说是个很好的选择！建议你先确定以下几个要素：     │ │   │
│         │ │ │ 1. 修炼体系设定                                             │ │   │
│         │ │ │ 2. 世界观架构                                               │ │   │
│         │ │ │ 3. 主角性格特点                                             │ │   │
│         │ │ │ 4. 主要冲突线索                                             │ │   │
│         │ │ │ 时间: 2025-01-29 10:31                                      │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │ 用户: 能帮我设计一个修炼体系吗？                             │ │   │
│         │ │ │ 时间: 2025-01-29 10:32                                      │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ │                                                                 │   │
│         │ │ [更多聊天记录...]                                               │   │
│         │ └─────────────────────────────────────────────────────────────────┘   │
│         │                                                                      │
│         │ ┌─────────────────────────────────────────────────────────────────┐   │
│         │ │                        输入区域                                 │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │ 请输入您的问题...                                           │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │                                                             │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────┐ ┌─────────────────────┐ ┌─────────────┐ │   │
│         │ │ │      发送消息       │ │      清空聊天       │ │  导出聊天   │ │   │
│         │ │ └─────────────────────┘ └─────────────────────┘ └─────────────┘ │   │
│         │ └─────────────────────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.11 设置界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              顶部工具栏                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 左侧导航 │                          设置                                        │
│         │                                                                      │
│         ├──────────────────────────────────────────────────────────────────────┤
│         │                            设置界面                                  │
│         │                                                                      │
│         │ ┌─────────────────────────────────────────────────────────────────┐   │
│         │ │                        API设置                                  │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │                    OpenAI设置                               │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ API密钥: ┌─────────────────────────────────────────────────┐ │ │   │
│         │ │ │         │ sk-...                                          │ │ │   │
│         │ │ │         └─────────────────────────────────────────────────┘ │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ 模型名称: ┌─────────────────────────────────────────────────┐ │ │   │
│         │ │ │          │ gpt-4-turbo                                     │ │ │   │
│         │ │ │          └─────────────────────────────────────────────────┘ │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │ │   │
│         │ │ │ │    测试连接     │ │      保存       │ │      重置       │ │ │   │
│         │ │ │ └─────────────────┘ └─────────────────┘ └─────────────────┘ │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │                   Claude设置                                │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ API密钥: ┌─────────────────────────────────────────────────┐ │ │   │
│         │ │ │         │ sk-ant-...                                      │ │ │   │
│         │ │ │         └─────────────────────────────────────────────────┘ │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ 模型名称: ┌─────────────────────────────────────────────────┐ │ │   │
│         │ │ │          │ claude-3-opus-20240229                          │ │ │   │
│         │ │ │          └─────────────────────────────────────────────────┘ │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │ │   │
│         │ │ │ │    测试连接     │ │      保存       │ │      重置       │ │ │   │
│         │ │ │ └─────────────────┘ └─────────────────┘ └─────────────────┘ │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────────────────────────────────────────────────┐ │   │
│         │ │ │                   自定义OpenAI设置                          │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ API密钥: ┌─────────────────────────────────────────────────┐ │ │   │
│         │ │ │         │                                                 │ │ │   │
│         │ │ │         └─────────────────────────────────────────────────┘ │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ 模型名称: ┌─────────────────────────────────────────────────┐ │ │   │
│         │ │ │          │                                                 │ │ │   │
│         │ │ │          └─────────────────────────────────────────────────┘ │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ API地址: ┌─────────────────────────────────────────────────┐ │ │   │
│         │ │ │         │ https://api.example.com/v1/chat/completions     │ │ │   │
│         │ │ │         └─────────────────────────────────────────────────┘ │ │   │
│         │ │ │                                                             │ │   │
│         │ │ │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │ │   │
│         │ │ │ │    测试连接     │ │      保存       │ │      重置       │ │ │   │
│         │ │ │ └─────────────────┘ └─────────────────┘ └─────────────────┘ │ │   │
│         │ │ └─────────────────────────────────────────────────────────────┘ │   │
│         │ │                                                                 │   │
│         │ │ [其他API设置: Gemini, ModelScope, SiliconFlow, Ollama...]        │   │
│         │ └─────────────────────────────────────────────────────────────────┘   │
│         │                                                                      │
│         │ ┌─────────────────────────────────────────────────────────────────┐   │
│         │ │                        应用设置                                 │   │
│         │ │                                                                 │   │
│         │ │ 主题: ┌─────────────────┐ 语言: ┌─────────────────┐             │   │
│         │ │      │ 明亮主题 ▼      │      │ 中文 ▼          │             │   │
│         │ │      └─────────────────┘      └─────────────────┘             │   │
│         │ │                                                                 │   │
│         │ │ 字体大小: ┌─────┐ 自动保存: ☑ 启用                              │   │
│         │ │          │ 14  │                                               │   │
│         │ │          └─────┘                                               │   │
│         │ │                                                                 │   │
│         │ │ 自动保存间隔: ┌─────┐ 秒                                        │   │
│         │ │              │ 30  │                                           │   │
│         │ │              └─────┘                                           │   │
│         │ │                                                                 │   │
│         │ │ ┌─────────────────┐ ┌─────────────────┐                        │   │
│         │ │ │      保存       │ │      重置       │                        │   │
│         │ │ └─────────────────┘ └─────────────────┘                        │   │
│         │ └─────────────────────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            底部状态栏                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 Glassmorphism UI设计规范

#### 4.2.1 设计原则
- **毛玻璃效果**: 使用backdrop-filter: blur()实现半透明毛玻璃效果
- **层次感**: 通过不同的透明度和阴影营造层次感
- **色彩搭配**: 明亮主题使用清新的蓝绿色系，暗黑主题使用深色调
- **圆角设计**: 统一使用8px-16px圆角，营造现代感
- **微交互**: 按钮悬停、点击状态的平滑过渡动画

#### 4.2.2 颜色规范

**明亮主题色彩**:
```css
:root {
  --primary-bg: rgba(255, 255, 255, 0.25);
  --secondary-bg: rgba(255, 255, 255, 0.15);
  --accent-color: #00D4FF;
  --success-color: #00C851;
  --warning-color: #FFB347;
  --error-color: #FF4444;
  --text-primary: #2C3E50;
  --text-secondary: #7F8C8D;
  --border-color: rgba(255, 255, 255, 0.3);
  --shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}
```

**暗黑主题色彩**:
```css
:root[data-theme="dark"] {
  --primary-bg: rgba(30, 30, 30, 0.25);
  --secondary-bg: rgba(20, 20, 20, 0.15);
  --accent-color: #64FFDA;
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --text-primary: #FFFFFF;
  --text-secondary: #B0BEC5;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow: 0 8px 32px rgba(0, 0, 0, 0.37);
}
```

#### 4.2.3 组件样式规范

**按钮样式**:
```css
.glass-button {
  background: var(--primary-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: var(--secondary-bg);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
}
```

**卡片样式**:
```css
.glass-card {
  background: var(--primary-bg);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: var(--shadow);
  padding: 24px;
}
```

---

## 5. 数据结构设计

### 5.1 核心数据模型

#### 5.1.1 项目文件结构 (.ainovel格式)
```typescript
interface NovelProject {
  // 项目元信息
  metadata: {
    id: string;
    name: string;
    version: string;
    createdAt: Date;
    updatedAt: Date;
    author: string;
    description?: string;
  };

  // 小说基本信息
  novel: {
    title: string;
    genre: string;
    theme: string;
    style: string;
    synopsis: string;
    worldSetting: string;
    coreTheme: string;
  };

  // 章节数据
  chapters: Chapter[];

  // 人物数据
  characters: Character[];

  // 人物关系
  relationships: Relationship[];

  // 上下文管理
  context: {
    plotThreads: PlotThread[];
    foreshadowing: Foreshadowing[];
    characterArcs: CharacterArc[];
    worldElements: WorldElement[];
  };

  // 统计信息
  statistics: NovelStatistics;

  // 用户设置
  settings: ProjectSettings;
}
```

#### 5.1.2 数据库设计 (SQLite)
```sql
-- 项目表
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_opened DATETIME,
  is_favorite BOOLEAN DEFAULT FALSE
);

-- 最近使用的项目
CREATE TABLE recent_projects (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  project_id TEXT NOT NULL,
  opened_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- API配置表
CREATE TABLE api_configs (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  provider TEXT NOT NULL,
  api_key TEXT,
  api_url TEXT,
  model TEXT,
  is_active BOOLEAN DEFAULT FALSE,
  test_status TEXT DEFAULT 'untested',
  last_tested DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 提示词模板表
CREATE TABLE prompt_templates (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  content TEXT NOT NULL,
  variables TEXT, -- JSON格式存储变量定义
  is_built_in BOOLEAN DEFAULT FALSE,
  usage_count INTEGER DEFAULT 0,
  rating REAL DEFAULT 0,
  tags TEXT, -- JSON格式存储标签
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 聊天会话表
CREATE TABLE chat_sessions (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  model TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 聊天消息表
CREATE TABLE chat_messages (
  id TEXT PRIMARY KEY,
  session_id TEXT NOT NULL,
  role TEXT NOT NULL, -- 'user' | 'assistant'
  content TEXT NOT NULL,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  model TEXT,
  tokens INTEGER,
  FOREIGN KEY (session_id) REFERENCES chat_sessions(id)
);

-- 向量文档表
CREATE TABLE vector_documents (
  id TEXT PRIMARY KEY,
  content TEXT NOT NULL,
  type TEXT NOT NULL, -- 'chapter' | 'character' | 'outline' | 'note'
  source_id TEXT NOT NULL,
  vector BLOB, -- 存储向量数据
  metadata TEXT, -- JSON格式存储元数据
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 5.2 配置文件结构

#### 5.2.1 应用配置 (config.json)
```json
{
  "app": {
    "version": "2.0.0",
    "theme": "light",
    "language": "zh-CN",
    "autoSave": true,
    "autoSaveInterval": 30,
    "windowSize": {
      "width": 1400,
      "height": 900
    },
    "windowPosition": {
      "x": 100,
      "y": 100
    }
  },
  "editor": {
    "fontSize": 14,
    "wordWrap": true,
    "showWordCount": true,
    "spellCheck": false,
    "aiAssistanceLevel": "moderate"
  },
  "backup": {
    "enabled": true,
    "interval": 10,
    "maxBackups": 10,
    "location": "./backups"
  },
  "logging": {
    "level": "info",
    "maxFiles": 7,
    "maxSize": "10m"
  }
}
```

---

## 6. API集成方案

### 6.1 AI模型集成架构

#### 6.1.1 统一API接口设计
```typescript
interface AIProvider {
  name: string;
  type: 'openai' | 'anthropic' | 'google' | 'modelscope' | 'ollama' | 'siliconflow' | 'custom';

  // 基础配置
  configure(config: APIConfig): void;

  // 连接测试
  testConnection(): Promise<boolean>;

  // 文本生成
  generateText(prompt: string, options?: GenerationOptions): Promise<GenerationResult>;

  // 流式生成
  generateTextStream(prompt: string, options?: GenerationOptions): AsyncGenerator<string>;

  // 聊天对话
  chat(messages: ChatMessage[], options?: ChatOptions): Promise<ChatMessage>;

  // 获取模型信息
  getModelInfo(): Promise<ModelInfo>;
}

interface GenerationOptions {
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stopSequences?: string[];
  presencePenalty?: number;
  frequencyPenalty?: number;
}

interface GenerationResult {
  content: string;
  finishReason: 'stop' | 'length' | 'content_filter';
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}
```

#### 6.1.2 各AI提供商实现

**OpenAI集成**:
```typescript
class OpenAIProvider implements AIProvider {
  private client: OpenAI;

  configure(config: APIConfig) {
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.apiUrl || 'https://api.openai.com/v1'
    });
  }

  async generateText(prompt: string, options?: GenerationOptions): Promise<GenerationResult> {
    const response = await this.client.chat.completions.create({
      model: this.config.model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: options?.maxTokens || 2000,
      temperature: options?.temperature || 0.7,
      top_p: options?.topP || 1,
      stop: options?.stopSequences,
      presence_penalty: options?.presencePenalty || 0,
      frequency_penalty: options?.frequencyPenalty || 0
    });

    return {
      content: response.choices[0].message.content,
      finishReason: response.choices[0].finish_reason,
      usage: {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens
      }
    };
  }

  async *generateTextStream(prompt: string, options?: GenerationOptions): AsyncGenerator<string> {
    const stream = await this.client.chat.completions.create({
      model: this.config.model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: options?.maxTokens || 2000,
      temperature: options?.temperature || 0.7,
      stream: true
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        yield content;
      }
    }
  }
}
```

**Claude集成**:
```typescript
class ClaudeProvider implements AIProvider {
  private client: Anthropic;

  configure(config: APIConfig) {
    this.client = new Anthropic({
      apiKey: config.apiKey
    });
  }

  async generateText(prompt: string, options?: GenerationOptions): Promise<GenerationResult> {
    const response = await this.client.messages.create({
      model: this.config.model,
      max_tokens: options?.maxTokens || 2000,
      temperature: options?.temperature || 0.7,
      top_p: options?.topP || 1,
      stop_sequences: options?.stopSequences,
      messages: [{ role: 'user', content: prompt }]
    });

    return {
      content: response.content[0].text,
      finishReason: response.stop_reason,
      usage: {
        promptTokens: response.usage.input_tokens,
        completionTokens: response.usage.output_tokens,
        totalTokens: response.usage.input_tokens + response.usage.output_tokens
      }
    };
  }
}
```

### 6.2 API管理服务

#### 6.2.1 API管理器
```typescript
class APIManager {
  private providers: Map<string, AIProvider> = new Map();
  private activeProvider: string | null = null;

  // 注册AI提供商
  registerProvider(name: string, provider: AIProvider) {
    this.providers.set(name, provider);
  }

  // 设置活跃提供商
  setActiveProvider(name: string) {
    if (this.providers.has(name)) {
      this.activeProvider = name;
    }
  }

  // 获取当前活跃提供商
  getActiveProvider(): AIProvider | null {
    if (this.activeProvider && this.providers.has(this.activeProvider)) {
      return this.providers.get(this.activeProvider)!;
    }
    return null;
  }

  // 测试所有配置的API
  async testAllConnections(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();

    for (const [name, provider] of this.providers) {
      try {
        const isConnected = await provider.testConnection();
        results.set(name, isConnected);
      } catch (error) {
        results.set(name, false);
      }
    }

    return results;
  }

  // 智能API地址修正
  correctAPIUrl(url: string, provider: string): string {
    const corrections = {
      openai: {
        suffixes: ['/v1/chat/completions', '/v1'],
        defaultSuffix: '/v1'
      },
      anthropic: {
        suffixes: ['/v1/messages'],
        defaultSuffix: '/v1/messages'
      },
      custom: {
        suffixes: ['/v1/chat/completions', '/chat/completions'],
        defaultSuffix: '/v1/chat/completions'
      }
    };

    const config = corrections[provider];
    if (!config) return url;

    // 检查URL是否已经有正确的后缀
    const hasValidSuffix = config.suffixes.some(suffix => url.endsWith(suffix));

    if (!hasValidSuffix) {
      // 移除可能的错误后缀并添加正确后缀
      let correctedUrl = url.replace(/\/+$/, ''); // 移除末尾斜杠
      correctedUrl += config.defaultSuffix;
      return correctedUrl;
    }

    return url;
  }
}
```

#### 6.2.2 降AI味处理
```typescript
class AIContentProcessor {
  // 降AI味处理规则
  private aiPatterns = [
    // 常见AI写作模式
    /让我们一起来/g,
    /总的来说/g,
    /综上所述/g,
    /值得注意的是/g,
    /需要强调的是/g,
    /毫无疑问/g,
    /显而易见/g,
    /不可否认/g,

    // 过度修饰词
    /非常非常/g,
    /极其/g,
    /十分/g,
    /相当/g,

    // 机械化表达
    /首先.*其次.*最后/g,
    /第一.*第二.*第三/g,
  ];

  // 人性化替换词库
  private humanReplacements = new Map([
    ['让我们一起来', ''],
    ['总的来说', ''],
    ['综上所述', ''],
    ['值得注意的是', ''],
    ['需要强调的是', ''],
    ['毫无疑问', '当然'],
    ['显而易见', '很明显'],
    ['不可否认', '确实'],
    ['非常非常', '很'],
    ['极其', '很'],
    ['十分', '很'],
    ['相当', '挺'],
  ]);

  // 降AI味处理
  reduceAIFlavor(content: string): string {
    let processed = content;

    // 应用模式替换
    for (const [pattern, replacement] of this.humanReplacements) {
      const regex = new RegExp(pattern, 'g');
      processed = processed.replace(regex, replacement);
    }

    // 移除过度的标点符号
    processed = processed.replace(/！{2,}/g, '！');
    processed = processed.replace(/？{2,}/g, '？');
    processed = processed.replace(/。{2,}/g, '。');

    // 简化复杂句式
    processed = this.simplifyComplexSentences(processed);

    // 增加口语化表达
    processed = this.addColloquialExpressions(processed);

    return processed;
  }

  private simplifyComplexSentences(content: string): string {
    // 简化过长的句子
    return content.replace(/([^。！？]{50,}?)，([^。！？]{50,})/g, '$1。$2');
  }

  private addColloquialExpressions(content: string): string {
    // 添加一些口语化的连接词
    const colloquialConnectors = ['不过', '话说', '说起来', '对了'];

    // 在适当位置插入口语化表达
    return content.replace(/。([^。！？]{20,})/g, (match, p1) => {
      if (Math.random() < 0.3) { // 30%概率添加口语化连接词
        const connector = colloquialConnectors[Math.floor(Math.random() * colloquialConnectors.length)];
        return `。${connector}，${p1}`;
      }
      return match;
    });
  }
}
```

---

## 7. 文件结构

### 7.1 项目目录结构
```
ai-novel-assistant/
├── electron/                          # Electron主进程
│   ├── main.ts                        # 主进程入口
│   ├── preload.ts                     # 预加载脚本
│   ├── menu.ts                        # 应用菜单
│   ├── window.ts                      # 窗口管理
│   ├── file-system.ts                 # 文件系统操作
│   ├── api-gateway.ts                 # API网关
│   └── database/                      # 数据库相关
│       ├── connection.ts              # 数据库连接
│       ├── migrations/                # 数据库迁移
│       └── models/                    # 数据模型
├── src/                               # 前端源码
│   ├── main.ts                        # Vue应用入口
│   ├── App.vue                        # 根组件
│   ├── components/                    # 组件目录
│   │   ├── common/                    # 通用组件
│   │   │   ├── GlassButton.vue        # 毛玻璃按钮
│   │   │   ├── GlassCard.vue          # 毛玻璃卡片
│   │   │   ├── GlassInput.vue         # 毛玻璃输入框
│   │   │   ├── GlassSelect.vue        # 毛玻璃选择器
│   │   │   ├── LoadingSpinner.vue     # 加载动画
│   │   │   ├── ProgressBar.vue        # 进度条
│   │   │   ├── Toast.vue              # 气泡通知
│   │   │   └── Modal.vue              # 模态框
│   │   ├── layout/                    # 布局组件
│   │   │   ├── AppHeader.vue          # 顶部工具栏
│   │   │   ├── AppSidebar.vue         # 左侧导航
│   │   │   ├── AppFooter.vue          # 底部状态栏
│   │   │   └── MainLayout.vue         # 主布局
│   │   ├── outline/                   # 大纲相关组件
│   │   │   ├── OutlineGenerator.vue   # 大纲生成器
│   │   │   ├── OutlineEditor.vue      # 大纲编辑器
│   │   │   ├── PromptTemplateManager.vue # 提示词模板管理
│   │   │   └── ModelSelector.vue      # 模型选择器
│   │   ├── chapter/                   # 章节相关组件
│   │   │   ├── ChapterList.vue        # 章节列表
│   │   │   ├── ChapterEditor.vue      # 章节编辑器
│   │   │   ├── ChapterGenerator.vue   # 章节生成器
│   │   │   ├── ChapterAnalyzer.vue    # 章节分析器
│   │   │   └── ContentEditor.vue      # 内容编辑器
│   │   ├── character/                 # 人物相关组件
│   │   │   ├── CharacterList.vue      # 人物列表
│   │   │   ├── CharacterEditor.vue    # 人物编辑器
│   │   │   ├── RelationshipGraph.vue  # 人物关系图
│   │   │   └── CharacterGenerator.vue # 人物生成器
│   │   ├── statistics/                # 统计相关组件
│   │   │   ├── StatisticsOverview.vue # 统计概览
│   │   │   ├── ChapterStats.vue       # 章节统计
│   │   │   └── ProgressChart.vue      # 进度图表
│   │   ├── chat/                      # 聊天相关组件
│   │   │   ├── ChatInterface.vue      # 聊天界面
│   │   │   ├── MessageList.vue        # 消息列表
│   │   │   └── MessageInput.vue       # 消息输入
│   │   ├── prompt/                    # 提示词相关组件
│   │   │   ├── PromptLibrary.vue      # 提示词库
│   │   │   ├── PromptEditor.vue       # 提示词编辑器
│   │   │   └── PromptCategory.vue     # 提示词分类
│   │   ├── context/                   # 上下文相关组件
│   │   │   ├── ContextManager.vue     # 上下文管理器
│   │   │   ├── PlotThreads.vue        # 情节线索
│   │   │   └── ForeshadowingTracker.vue # 伏笔追踪
│   │   ├── vector/                    # 向量检索组件
│   │   │   ├── VectorSearch.vue       # 向量搜索
│   │   │   ├── EmbeddingConfig.vue    # 嵌入配置
│   │   │   └── SearchResults.vue      # 搜索结果
│   │   └── settings/                  # 设置相关组件
│   │       ├── APISettings.vue        # API设置
│   │       ├── UISettings.vue         # 界面设置
│   │       ├── EditorSettings.vue     # 编辑器设置
│   │       └── GeneralSettings.vue    # 通用设置
│   ├── views/                         # 页面视图
│   │   ├── Dashboard.vue              # 仪表盘
│   │   ├── OutlineGeneration.vue      # 大纲生成页面
│   │   ├── OutlineEditing.vue         # 大纲编辑页面
│   │   ├── ChapterEditing.vue         # 章节编辑页面
│   │   ├── ChapterGeneration.vue      # 章节生成页面
│   │   ├── ChapterAnalysis.vue        # 章节分析页面
│   │   ├── CharacterManagement.vue    # 人物管理页面
│   │   ├── RelationshipGraph.vue      # 人物关系图页面
│   │   ├── Statistics.vue             # 统计信息页面
│   │   ├── AIChat.vue                 # AI聊天页面
│   │   ├── PromptLibrary.vue          # 提示词库页面
│   │   ├── ContextManagement.vue      # 上下文管理页面
│   │   ├── VectorRetrieval.vue        # 向量检索页面
│   │   └── Settings.vue               # 设置页面
│   ├── stores/                        # 状态管理
│   │   ├── index.ts                   # Store入口
│   │   ├── app.ts                     # 应用状态
│   │   ├── project.ts                 # 项目状态
│   │   ├── outline.ts                 # 大纲状态
│   │   ├── chapter.ts                 # 章节状态
│   │   ├── character.ts               # 人物状态
│   │   ├── chat.ts                    # 聊天状态
│   │   ├── prompt.ts                  # 提示词状态
│   │   ├── context.ts                 # 上下文状态
│   │   ├── vector.ts                  # 向量状态
│   │   └── settings.ts                # 设置状态
│   ├── services/                      # 服务层
│   │   ├── api/                       # API服务
│   │   │   ├── base.ts                # 基础API类
│   │   │   ├── openai.ts              # OpenAI服务
│   │   │   ├── claude.ts              # Claude服务
│   │   │   ├── gemini.ts              # Gemini服务
│   │   │   ├── modelscope.ts          # ModelScope服务
│   │   │   ├── ollama.ts              # Ollama服务
│   │   │   ├── siliconflow.ts         # SiliconFlow服务
│   │   │   └── custom.ts              # 自定义API服务
│   │   ├── database/                  # 数据库服务
│   │   │   ├── project.ts             # 项目数据服务
│   │   │   ├── template.ts            # 模板数据服务
│   │   │   ├── chat.ts                # 聊天数据服务
│   │   │   └── vector.ts              # 向量数据服务
│   │   ├── file/                      # 文件服务
│   │   │   ├── project-file.ts        # 项目文件服务
│   │   │   ├── export.ts              # 导出服务
│   │   │   └── backup.ts              # 备份服务
│   │   ├── ai/                        # AI服务
│   │   │   ├── manager.ts             # AI管理器
│   │   │   ├── processor.ts           # 内容处理器
│   │   │   ├── context.ts             # 上下文服务
│   │   │   └── vector.ts              # 向量服务
│   │   └── utils/                     # 工具服务
│   │       ├── logger.ts              # 日志服务
│   │       ├── validator.ts           # 验证服务
│   │       ├── formatter.ts           # 格式化服务
│   │       └── notification.ts        # 通知服务
│   ├── router/                        # 路由配置
│   │   └── index.ts                   # 路由定义
│   ├── styles/                        # 样式文件
│   │   ├── main.css                   # 主样式
│   │   ├── glassmorphism.css          # 毛玻璃样式
│   │   ├── themes/                    # 主题样式
│   │   │   ├── light.css              # 明亮主题
│   │   │   └── dark.css               # 暗黑主题
│   │   └── components/                # 组件样式
│   ├── assets/                        # 静态资源
│   │   ├── icons/                     # SVG图标
│   │   ├── images/                    # 图片资源
│   │   └── fonts/                     # 字体文件
│   ├── types/                         # TypeScript类型定义
│   │   ├── index.ts                   # 类型入口
│   │   ├── api.ts                     # API类型
│   │   ├── project.ts                 # 项目类型
│   │   ├── chapter.ts                 # 章节类型
│   │   ├── character.ts               # 人物类型
│   │   └── settings.ts                # 设置类型
│   └── utils/                         # 工具函数
│       ├── index.ts                   # 工具入口
│       ├── date.ts                    # 日期工具
│       ├── string.ts                  # 字符串工具
│       ├── file.ts                    # 文件工具
│       └── validation.ts              # 验证工具
├── public/                            # 公共资源
│   ├── index.html                     # HTML模板
│   └── favicon.ico                    # 应用图标
├── dist/                              # 构建输出
├── resources/                         # 资源文件
│   ├── icons/                         # 应用图标
│   ├── templates/                     # 内置模板
│   │   ├── prompts/                   # 提示词模板
│   │   └── novels/                    # 小说模板
│   └── data/                          # 初始数据
│       ├── genres.json                # 小说类型
│       ├── themes.json                # 小说主题
│       └── styles.json                # 小说风格
├── tests/                             # 测试文件
│   ├── unit/                          # 单元测试
│   ├── integration/                   # 集成测试
│   └── e2e/                           # 端到端测试
├── docs/                              # 文档
│   ├── api.md                         # API文档
│   ├── development.md                 # 开发文档
│   └── user-guide.md                  # 用户指南
├── scripts/                           # 构建脚本
│   ├── build.js                       # 构建脚本
│   ├── dev.js                         # 开发脚本
│   └── package.js                     # 打包脚本
├── package.json                       # 项目配置
├── tsconfig.json                      # TypeScript配置
├── vite.config.ts                     # Vite配置
├── electron-builder.config.js         # Electron Builder配置
├── tailwind.config.js                 # Tailwind配置
├── .eslintrc.js                       # ESLint配置
├── .prettierrc                        # Prettier配置
├── .gitignore                         # Git忽略文件
└── README.md                          # 项目说明
```

---

## 8. 开发路线图

### 8.1 开发阶段规划

#### 8.1.1 第一阶段：基础架构搭建 (1-2周)
**目标**: 建立项目基础架构和核心框架

**主要任务**:
1. **项目初始化**
   - 创建Electron + Vue 3项目结构
   - 配置TypeScript、Vite、Tailwind CSS
   - 设置ESLint、Prettier代码规范
   - 配置Electron Builder打包工具

2. **基础UI框架**
   - 实现Glassmorphism设计系统
   - 创建通用组件库 (按钮、卡片、输入框等)
   - 实现明亮/暗黑双主题切换
   - 搭建主布局结构 (顶部工具栏、侧边栏、状态栏)

3. **数据层建设**
   - 设计SQLite数据库结构
   - 实现Prisma ORM配置
   - 创建基础数据模型
   - 实现数据库迁移机制

4. **状态管理**
   - 配置Pinia状态管理
   - 创建核心Store模块
   - 实现状态持久化

**交付物**:
- 可运行的基础应用框架
- 完整的UI组件库
- 数据库结构和基础CRUD操作
- 主题切换功能

#### 8.1.2 第二阶段：核心功能开发 (3-4周)
**目标**: 实现AI小说创作的核心功能

**主要任务**:
1. **AI集成模块**
   - 实现统一AI接口抽象
   - 集成OpenAI、Claude、Gemini API
   - 实现API配置管理和连接测试
   - 开发智能API地址修正功能

2. **大纲生成功能**
   - 实现大纲生成界面
   - 开发提示词模板系统
   - 实现模型选择和参数配置
   - 创建大纲编辑功能

3. **章节管理功能**
   - 实现章节列表管理
   - 开发章节编辑器
   - 实现章节内容生成
   - 创建章节分析功能

4. **人物管理功能**
   - 实现人物信息管理
   - 开发人物关系图
   - 实现AI人物生成
   - 创建人物关系编辑

**交付物**:
- 完整的大纲生成和编辑功能
- 章节管理和内容生成功能
- 人物管理和关系图功能
- 多AI模型集成和管理

#### 8.1.3 第三阶段：高级功能开发 (2-3周)
**目标**: 实现高级AI辅助功能和用户体验优化

**主要任务**:
1. **上下文管理系统**
   - 实现情节线索追踪
   - 开发伏笔管理功能
   - 创建角色一致性检查
   - 实现上下文自动检索

2. **向量检索系统**
   - 集成文本嵌入模型
   - 实现向量数据库
   - 开发相似内容检索
   - 创建智能推荐功能

3. **AI聊天功能**
   - 实现AI对话界面
   - 开发聊天记录管理
   - 创建写作助手功能
   - 实现模型可用性验证

4. **提示词库系统**
   - 内置丰富的提示词模板
   - 实现自定义提示词管理
   - 开发提示词分类和搜索
   - 创建提示词评分系统

**交付物**:
- 完整的上下文管理系统
- 向量检索和智能推荐功能
- AI聊天和写作助手功能
- 丰富的提示词库系统

#### 8.1.4 第四阶段：功能完善和优化 (2-3周)
**目标**: 完善所有功能并进行性能优化

**主要任务**:
1. **统计分析功能**
   - 实现创作进度统计
   - 开发字数和完成度分析
   - 创建可视化图表
   - 实现数据导出功能

2. **文件管理系统**
   - 实现.ainovel项目文件格式
   - 开发项目保存和加载
   - 创建自动备份功能
   - 实现多格式导出

3. **降AI味处理**
   - 开发AI内容识别算法
   - 实现文本人性化处理
   - 创建写作风格优化
   - 实现内容质量评估

4. **用户体验优化**
   - 实现窗口状态记忆
   - 开发快捷键支持
   - 创建操作撤销/重做
   - 实现拖拽排序功能

**交付物**:
- 完整的统计分析功能
- 项目文件管理系统
- 降AI味处理功能
- 优化的用户体验

#### 8.1.5 第五阶段：测试和发布准备 (1-2周)
**目标**: 全面测试和发布准备

**主要任务**:
1. **全面测试**
   - 单元测试覆盖
   - 集成测试验证
   - 用户界面测试
   - 性能压力测试

2. **文档编写**
   - 用户使用手册
   - API接口文档
   - 开发者文档
   - 部署指南

3. **打包发布**
   - 配置Electron Builder
   - 创建安装程序
   - 代码签名配置
   - 自动更新机制

4. **部署优化**
   - 资源文件优化
   - 启动性能优化
   - 内存使用优化
   - 错误处理完善

**交付物**:
- 完整的测试报告
- 用户和开发文档
- 可发布的安装包
- 部署和更新机制

### 8.2 技术实现优先级

#### 8.2.1 高优先级功能
1. **基础架构** - 项目框架、UI组件、数据库
2. **AI集成** - 多模型支持、API管理
3. **大纲生成** - 核心创作功能
4. **章节管理** - 内容编辑和生成
5. **人物管理** - 角色设定和关系

#### 8.2.2 中优先级功能
1. **上下文管理** - 情节追踪、伏笔管理
2. **AI聊天** - 写作助手功能
3. **提示词库** - 模板管理系统
4. **统计分析** - 进度和数据分析
5. **文件管理** - 项目保存和导出

#### 8.2.3 低优先级功能
1. **向量检索** - 智能推荐功能
2. **降AI味** - 内容优化处理
3. **高级设置** - 个性化配置
4. **性能优化** - 速度和内存优化
5. **扩展功能** - 插件系统等

### 8.3 风险控制和应对策略

#### 8.3.1 技术风险
**风险**: AI API限制和费用控制
**应对**:
- 实现多API提供商支持
- 添加使用量监控和限制
- 提供本地模型选项 (Ollama)

**风险**: 性能问题和内存占用
**应对**:
- 实现懒加载和虚拟滚动
- 优化数据结构和算法
- 添加性能监控和优化

#### 8.3.2 开发风险
**风险**: 功能复杂度过高
**应对**:
- 采用模块化开发方式
- 实现MVP (最小可行产品)
- 逐步迭代和完善功能

**风险**: 跨平台兼容性问题
**应对**:
- 使用成熟的跨平台技术栈
- 在多平台进行测试验证
- 提供平台特定的优化

---

## 9. 部署打包

### 9.1 构建配置

#### 9.1.1 Electron Builder配置
```javascript
// electron-builder.config.js
module.exports = {
  appId: 'com.ainovel.assistant',
  productName: 'AI小说助手',
  copyright: 'Copyright © 2025 AI Novel Assistant',

  directories: {
    output: 'dist-electron',
    buildResources: 'build'
  },

  files: [
    'dist/**/*',
    'electron/**/*',
    'resources/**/*',
    'node_modules/**/*',
    '!node_modules/**/test/**/*',
    '!node_modules/**/*.d.ts'
  ],

  extraResources: [
    {
      from: 'resources/templates',
      to: 'templates'
    },
    {
      from: 'resources/data',
      to: 'data'
    }
  ],

  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64', 'ia32']
      },
      {
        target: 'msi',
        arch: ['x64']
      }
    ],
    icon: 'build/icon.ico',
    requestedExecutionLevel: 'asInvoker',
    artifactName: '${productName}-${version}-${arch}.${ext}'
  },

  mac: {
    target: [
      {
        target: 'dmg',
        arch: ['x64', 'arm64']
      },
      {
        target: 'zip',
        arch: ['x64', 'arm64']
      }
    ],
    icon: 'build/icon.icns',
    category: 'public.app-category.productivity',
    artifactName: '${productName}-${version}-${arch}.${ext}'
  },

  linux: {
    target: [
      {
        target: 'AppImage',
        arch: ['x64']
      },
      {
        target: 'deb',
        arch: ['x64']
      },
      {
        target: 'rpm',
        arch: ['x64']
      }
    ],
    icon: 'build/icon.png',
    category: 'Office',
    artifactName: '${productName}-${version}-${arch}.${ext}'
  },

  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    allowElevation: true,
    installerIcon: 'build/installer.ico',
    uninstallerIcon: 'build/uninstaller.ico',
    installerHeaderIcon: 'build/installer.ico',
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: 'AI小说助手',
    include: 'build/installer.nsh'
  },

  dmg: {
    title: '${productName} ${version}',
    icon: 'build/volume.icns',
    background: 'build/background.png',
    window: {
      width: 540,
      height: 380
    },
    contents: [
      {
        x: 140,
        y: 200,
        type: 'file'
      },
      {
        x: 400,
        y: 200,
        type: 'link',
        path: '/Applications'
      }
    ]
  },

  publish: {
    provider: 'github',
    owner: 'ai-novel-assistant',
    repo: 'ai-novel-assistant'
  }
};
```

#### 9.1.2 构建脚本
```json
// package.json scripts
{
  "scripts": {
    "dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"",
    "dev:vite": "vite",
    "dev:electron": "electron-dev",
    "build": "npm run build:vite && npm run build:electron",
    "build:vite": "vite build",
    "build:electron": "electron-builder",
    "build:win": "electron-builder --win",
    "build:mac": "electron-builder --mac",
    "build:linux": "electron-builder --linux",
    "build:all": "electron-builder --win --mac --linux",
    "preview": "vite preview",
    "test": "vitest",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts",
    "lint:fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "format": "prettier --write src/",
    "type-check": "vue-tsc --noEmit"
  }
}
```

### 9.2 安装程序配置

#### 9.2.1 Windows安装程序 (NSIS)
```nsis
; build/installer.nsh
!macro customInstall
  ; 创建应用数据目录
  CreateDirectory "$APPDATA\AI小说助手"
  CreateDirectory "$APPDATA\AI小说助手\projects"
  CreateDirectory "$APPDATA\AI小说助手\backups"
  CreateDirectory "$APPDATA\AI小说助手\logs"

  ; 复制默认配置文件
  File /oname=$APPDATA\AI小说助手\config.json "${BUILD_RESOURCES_DIR}\config.default.json"

  ; 注册文件关联
  WriteRegStr HKCR ".ainovel" "" "AINovelProject"
  WriteRegStr HKCR "AINovelProject" "" "AI小说助手项目文件"
  WriteRegStr HKCR "AINovelProject\DefaultIcon" "" "$INSTDIR\AI小说助手.exe,0"
  WriteRegStr HKCR "AINovelProject\shell\open\command" "" '"$INSTDIR\AI小说助手.exe" "%1"'
!macroend

!macro customUnInstall
  ; 清理注册表
  DeleteRegKey HKCR ".ainovel"
  DeleteRegKey HKCR "AINovelProject"

  ; 询问是否删除用户数据
  MessageBox MB_YESNO "是否删除所有用户数据和项目文件？" IDNO skip_data_deletion
  RMDir /r "$APPDATA\AI小说助手"
  skip_data_deletion:
!macroend
```

#### 9.2.2 macOS应用程序包
```xml
<!-- build/Info.plist -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>CFBundleDisplayName</key>
  <string>AI小说助手</string>
  <key>CFBundleExecutable</key>
  <string>AI小说助手</string>
  <key>CFBundleIdentifier</key>
  <string>com.ainovel.assistant</string>
  <key>CFBundleName</key>
  <string>AI小说助手</string>
  <key>CFBundleVersion</key>
  <string>2.0.0</string>
  <key>CFBundleShortVersionString</key>
  <string>2.0.0</string>
  <key>CFBundleDocumentTypes</key>
  <array>
    <dict>
      <key>CFBundleTypeExtensions</key>
      <array>
        <string>ainovel</string>
      </array>
      <key>CFBundleTypeName</key>
      <string>AI小说助手项目文件</string>
      <key>CFBundleTypeRole</key>
      <string>Editor</string>
      <key>LSHandlerRank</key>
      <string>Owner</string>
    </dict>
  </array>
  <key>NSHighResolutionCapable</key>
  <true/>
  <key>LSMinimumSystemVersion</key>
  <string>10.14.0</string>
</dict>
</plist>
```

### 9.3 自动更新机制

#### 9.3.1 更新检查服务
```typescript
// src/services/updater.ts
import { app, autoUpdater, dialog } from 'electron';
import { logger } from './logger';

class UpdaterService {
  private updateServer = 'https://api.github.com/repos/ai-novel-assistant/ai-novel-assistant/releases/latest';

  constructor() {
    this.setupAutoUpdater();
  }

  private setupAutoUpdater() {
    autoUpdater.setFeedURL({
      url: this.updateServer,
      headers: {
        'User-Agent': `AI小说助手/${app.getVersion()}`
      }
    });

    autoUpdater.on('checking-for-update', () => {
      logger.info('正在检查更新...');
    });

    autoUpdater.on('update-available', () => {
      logger.info('发现新版本，开始下载...');
      this.showUpdateDialog('发现新版本', '正在下载更新，请稍候...');
    });

    autoUpdater.on('update-not-available', () => {
      logger.info('当前已是最新版本');
    });

    autoUpdater.on('error', (error) => {
      logger.error('更新检查失败:', error);
    });

    autoUpdater.on('update-downloaded', () => {
      this.showUpdateDialog(
        '更新已下载',
        '新版本已下载完成，是否立即重启应用以完成更新？',
        true
      );
    });
  }

  checkForUpdates() {
    autoUpdater.checkForUpdates();
  }

  private showUpdateDialog(title: string, message: string, showRestart = false) {
    const buttons = showRestart ? ['立即重启', '稍后重启'] : ['确定'];

    dialog.showMessageBox({
      type: 'info',
      title,
      message,
      buttons
    }).then((result) => {
      if (showRestart && result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  }
}

export const updaterService = new UpdaterService();
```

### 9.4 性能优化

#### 9.4.1 资源优化
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [vue()],

  build: {
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['vue', 'vue-router', 'pinia'],
          'ui': ['element-plus'],
          'ai': ['openai', '@anthropic-ai/sdk'],
          'utils': ['lodash', 'dayjs']
        }
      }
    },

    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },

    // 资源内联阈值
    assetsInlineLimit: 4096,

    // 启用源码映射
    sourcemap: false
  },

  // 别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@services': resolve(__dirname, 'src/services'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@types': resolve(__dirname, 'src/types')
    }
  },

  // 开发服务器配置
  server: {
    port: 3000,
    open: false
  }
});
```

---

## 10. 测试方案

### 10.1 测试策略

#### 10.1.1 测试金字塔
```
                    E2E测试
                 (用户场景测试)
                /              \
           集成测试              集成测试
        (API集成测试)        (组件集成测试)
       /                                    \
  单元测试                                单元测试
(工具函数测试)                          (组件单元测试)
```

#### 10.1.2 测试覆盖率目标
- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 60%
- **E2E测试覆盖率**: ≥ 40%
- **关键路径覆盖率**: 100%

### 10.2 单元测试

#### 10.2.1 工具函数测试
```typescript
// tests/unit/utils/string.test.ts
import { describe, it, expect } from 'vitest';
import { truncateText, countWords, removeAIFlavor } from '@/utils/string';

describe('字符串工具函数', () => {
  describe('truncateText', () => {
    it('应该正确截断长文本', () => {
      const text = '这是一段很长的文本内容，需要被截断处理';
      const result = truncateText(text, 10);
      expect(result).toBe('这是一段很长的文本...');
    });

    it('短文本不应该被截断', () => {
      const text = '短文本';
      const result = truncateText(text, 10);
      expect(result).toBe('短文本');
    });
  });

  describe('countWords', () => {
    it('应该正确统计中文字数', () => {
      const text = '这是一段中文文本，包含标点符号。';
      const result = countWords(text);
      expect(result).toBe(13);
    });

    it('应该正确统计英文单词数', () => {
      const text = 'This is an English text with punctuation.';
      const result = countWords(text);
      expect(result).toBe(7);
    });
  });

  describe('removeAIFlavor', () => {
    it('应该移除AI写作痕迹', () => {
      const text = '总的来说，这是一个非常非常好的想法。';
      const result = removeAIFlavor(text);
      expect(result).toBe('这是一个很好的想法。');
    });
  });
});
```

#### 10.2.2 组件单元测试
```typescript
// tests/unit/components/GlassButton.test.ts
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import GlassButton from '@/components/common/GlassButton.vue';

describe('GlassButton组件', () => {
  it('应该正确渲染按钮文本', () => {
    const wrapper = mount(GlassButton, {
      props: {
        text: '测试按钮'
      }
    });

    expect(wrapper.text()).toBe('测试按钮');
  });

  it('应该在点击时触发事件', async () => {
    const wrapper = mount(GlassButton, {
      props: {
        text: '点击测试'
      }
    });

    await wrapper.trigger('click');
    expect(wrapper.emitted('click')).toBeTruthy();
  });

  it('禁用状态下不应该触发点击事件', async () => {
    const wrapper = mount(GlassButton, {
      props: {
        text: '禁用按钮',
        disabled: true
      }
    });

    await wrapper.trigger('click');
    expect(wrapper.emitted('click')).toBeFalsy();
  });
});
```

### 10.3 集成测试

#### 10.3.1 API集成测试
```typescript
// tests/integration/api/openai.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { OpenAIProvider } from '@/services/api/openai';

describe('OpenAI API集成测试', () => {
  let provider: OpenAIProvider;

  beforeEach(() => {
    provider = new OpenAIProvider();
    provider.configure({
      apiKey: 'test-api-key',
      model: 'gpt-4',
      apiUrl: 'https://api.openai.com/v1'
    });
  });

  it('应该能够测试API连接', async () => {
    // Mock API响应
    vi.mock('openai', () => ({
      OpenAI: vi.fn().mockImplementation(() => ({
        chat: {
          completions: {
            create: vi.fn().mockResolvedValue({
              choices: [{ message: { content: 'Hello' } }],
              usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 }
            })
          }
        }
      }))
    }));

    const isConnected = await provider.testConnection();
    expect(isConnected).toBe(true);
  });

  it('应该能够生成文本内容', async () => {
    const result = await provider.generateText('写一个简短的故事');

    expect(result).toHaveProperty('content');
    expect(result).toHaveProperty('usage');
    expect(result.content).toBeTruthy();
  });
});
```

### 10.4 端到端测试

#### 10.4.1 用户场景测试
```typescript
// tests/e2e/novel-creation.spec.ts
import { test, expect } from '@playwright/test';

test.describe('小说创作流程', () => {
  test('完整的小说创作流程', async ({ page }) => {
    // 启动应用
    await page.goto('/');

    // 创建新项目
    await page.click('[data-testid="new-project-btn"]');
    await page.fill('[data-testid="project-name-input"]', '测试小说');
    await page.click('[data-testid="create-project-btn"]');

    // 生成大纲
    await page.click('[data-testid="outline-generation-tab"]');
    await page.fill('[data-testid="novel-title-input"]', '修仙传奇');
    await page.selectOption('[data-testid="genre-select"]', '玄幻修仙');
    await page.selectOption('[data-testid="theme-select"]', '成长冒险');
    await page.fill('[data-testid="chapter-count-input"]', '5');

    await page.click('[data-testid="generate-outline-btn"]');

    // 等待大纲生成完成
    await expect(page.locator('[data-testid="outline-result"]')).toBeVisible({ timeout: 30000 });

    // 编辑章节
    await page.click('[data-testid="chapter-editing-tab"]');
    await page.click('[data-testid="chapter-1"]');
    await page.fill('[data-testid="chapter-title-input"]', '初入修仙界');
    await page.fill('[data-testid="chapter-summary-textarea"]', '主角踏入修仙世界的第一步');

    // 生成章节内容
    await page.click('[data-testid="chapter-generation-tab"]');
    await page.selectOption('[data-testid="chapter-select"]', '第1章');
    await page.click('[data-testid="generate-chapter-btn"]');

    // 等待章节生成完成
    await expect(page.locator('[data-testid="chapter-content"]')).toBeVisible({ timeout: 30000 });

    // 保存项目
    await page.click('[data-testid="save-project-btn"]');

    // 验证保存成功
    await expect(page.locator('[data-testid="save-success-toast"]')).toBeVisible();
  });

  test('AI聊天功能测试', async ({ page }) => {
    await page.goto('/');

    // 进入AI聊天页面
    await page.click('[data-testid="ai-chat-tab"]');

    // 选择AI模型
    await page.selectOption('[data-testid="model-select"]', 'gpt-4');

    // 发送消息
    await page.fill('[data-testid="chat-input"]', '你好，我想写一部修仙小说');
    await page.click('[data-testid="send-message-btn"]');

    // 等待AI回复
    await expect(page.locator('[data-testid="ai-message"]').last()).toBeVisible({ timeout: 30000 });

    // 验证消息内容
    const aiMessage = await page.locator('[data-testid="ai-message"]').last().textContent();
    expect(aiMessage).toBeTruthy();
  });
});
```

### 10.5 性能测试

#### 10.5.1 加载性能测试
```typescript
// tests/performance/startup.test.ts
import { test, expect } from '@playwright/test';

test.describe('应用性能测试', () => {
  test('应用启动时间测试', async ({ page }) => {
    const startTime = Date.now();

    await page.goto('/');
    await page.waitForSelector('[data-testid="main-layout"]');

    const loadTime = Date.now() - startTime;

    // 应用启动时间应该在3秒内
    expect(loadTime).toBeLessThan(3000);
  });

  test('大文件加载性能测试', async ({ page }) => {
    await page.goto('/');

    // 创建包含大量章节的项目
    const startTime = Date.now();

    // 模拟加载大项目文件
    await page.evaluate(() => {
      const largeProject = {
        chapters: Array.from({ length: 100 }, (_, i) => ({
          id: `chapter-${i}`,
          title: `第${i + 1}章`,
          content: 'A'.repeat(5000) // 5KB内容
        }))
      };

      localStorage.setItem('large-project', JSON.stringify(largeProject));
    });

    await page.reload();
    await page.waitForSelector('[data-testid="main-layout"]');

    const loadTime = Date.now() - startTime;

    // 大文件加载时间应该在5秒内
    expect(loadTime).toBeLessThan(5000);
  });
});
```

### 10.6 测试自动化

#### 10.6.1 CI/CD配置
```yaml
# .github/workflows/test.yml
name: 测试流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: 设置Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: 安装依赖
      run: npm ci

    - name: 代码检查
      run: npm run lint

    - name: 类型检查
      run: npm run type-check

    - name: 单元测试
      run: npm run test:unit

    - name: 集成测试
      run: npm run test:integration

    - name: E2E测试
      run: npm run test:e2e

    - name: 生成测试报告
      run: npm run test:coverage

    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

---

## 总结

本开发文档详细描述了AI小说助手V2.0的完整开发方案，包括：

1. **技术架构**: 基于Electron + Vue 3的现代化桌面应用架构
2. **功能设计**: 涵盖大纲生成、章节编辑、人物管理等核心功能
3. **界面设计**: 采用Glassmorphism设计风格的现代化UI
4. **数据结构**: 完整的数据模型和存储方案
5. **API集成**: 支持多种AI模型的统一接口设计
6. **开发路线**: 分阶段的开发计划和风险控制
7. **部署方案**: 跨平台打包和发布策略
8. **测试策略**: 全面的测试覆盖和质量保证

该文档为开发团队提供了清晰的技术指导和实施路径，确保项目能够按计划高质量交付。
```
```
```
```
```
```
```
```
```
```
```