需要创建一个新的文档，然后请帮我详细编写一个AI小说助手开发文档，每个功能都要详细编写，Electron + Vue 3桌面应用，内置依赖，方便不会安装依赖的用户启动使用，不使用企业级应用程序技术，确保应用程序能够正常启动运行使用。

核心功能如下：
使用ai生成多章节的长篇，中长篇，短篇等网络小说，自动检索衔接上下文、伏笔。
功能模块	关键能力

大纲生成：包括以下功能
1.模型选择-AI模型（GPT，Claude，Gemini，自定义OpenAI，ModelScope，Ollama，SiliconFlow）

2.提示词模板：内置大纲提示词模板，自定义大纲提示词模板（用户可以新建，编辑，删除）
模板格式如：
模板名称：标准大纲提示词
模板描述：标准的小说大纲生成提示词
模板分类：大纲
模板内容：请为我创建一部小说的详细大纲，具体要求如下：
小说标题：[用户输入的标题]
小说类型：[用户输入的类型]
主题：[用户输入的主题]
风格：[用户输入的风格]

章节数：[用户设置的章节数]章
每章字数：[用户设置的字数]字

人物设置：
主角数量：[用户设置的主角数量]个
重要角色数量：[用户设置的重要角色数量]个
配角数量：[用户设置的配角数量] 个
龙套数量：[用户设置的龙套数量] 个

生成范围：从第[起始章]章 到 第[结束章]章

请生成以下内容：
*1. 小说标题
*2. 核心主题
*3. 主要人物（包括姓名、身份、性格特点和背景故事）
*4. 故事梗概
*5. 章节结构（每章包含标题、简介和具体章节）
*6. 世界观设定

特别要求：
*1. 章节标题必须包含章节号，如"第一章：章节标题"
*2. 只生成指定范围内的章节，但保持与已有大纲的一致性

请确保大纲结构完整、逻辑合理，并以JSON格式返回。

3.基本信息：
小说标题
小说类型（内置多个小说类型）
小说主题（内置多个小说主题）
小说风格（内置多个小说风格）
章节数：默认为1章，最小输入章节数为1，最大输入章节数为9999
每章字数：默认为3500字/章，最小输入字数为300，最大输入字数为9999

4.生成范围：
起始章：如1
结束章：如10

5.操作：
生成大纲，清空大纲

6.生成结果

--------------

大纲编辑：包括以下功能
根据大纲生成-如果不满意可以使用AI生成
小说标题-AI生成
中心思想-AI生成
故事梗概-AI生成
世界观设定-AI生成
最后，保存修改

章节编辑：包括以下功能
根据大纲生成-如果不满意可以使用AI生成
章节列表-添加章节
章节标题-AI生成
章节摘要-选择角色-AI生成
最后，保存修改

章节生成：包括以下功能
根据大纲生成-如果不满意可以使用AI生成
章节选择
章节信息
章节内容-下、选择角色-目标字数：[选填]-AI辅助编辑-选定文本润色
操作-保存章节

人物编辑：包括以下功能
根据大纲生成-如果不满意可以使用AI生成
角色列表-添加角色-编辑角色-删除角色-使用AI生成角色-模型选择（GPT，Claude，Gemini，自定义OpenAI，ModelScope，Ollama，SiliconFlow）
角色详情

人物关系图：包括以下功能
根据大纲生成
角色1：
角色2：
关系：例如：朋友、敌人、父子、恋人等
添加/更新关系
删除关系
保存关系（暂存）

章节分析：包括以下功能
根据大纲生成-如果不满意可以使用AI生成
章节选择
分析章节数：例如第1章
分析章节
分析选项-核心剧情分析-故事梗概提取-优缺点分析-角色标注-物品标注-改进建议
内容：核心剧情分析-故事梗概提取-优缺点分析-角色标注-物品标注-改进建议
章节改进

统计信息：包括以下功能
根据小说进度实时更新
概览-小说标题-章节数-总字数-平均每章字数-已完成章节-完成度
章节统计-章节号-章节标题-字数
刷新统计

设置：包括以下功能
API设置：
OpenAI API密钥（输入框）
模型名称（输入框）

Anthropic API密钥（输入框）
模型名称（输入框）

Google API密钥（输入框）
模型名称（输入框）

ModelScope API密钥（输入框）
模型名称：（输入框）

SiliconFlow API密钥（输入框）
模型名称（输入框）

自定义OpenAI兼容API设置：
API密钥：（输入框）
模型名称：（输入框）
API地址：（输入框）

Ollama本地模型设置：
API地址（输入框）

自定义模型管理-选择模型-选择自定义模型
添加模型-编辑模型-删除模型
测试API连接
最后保存设置-重置设置
OpenAI (GPT) 设置
[API_KEYS]
gpt_api_key = your_openai_api_key_here

[MODELS]
gpt_model = gpt-4-turbo  # 或 gpt-4o, gpt-3.5-turbo 等 # 或自由输入OpenAI 模型

Claude 设置
[API_KEYS]
claude_api_key = your_anthropic_api_key_here

[MODELS]
claude_model = claude-3-opus-20240229  # 或 claude-3-sonnet, claude-3-haiku 等  # 或自由输入Claude模型

Gemini 设置
[API_KEYS]
gemini_api_key = your_google_api_key_here

[MODELS]
gemini_model = gemini-2.0-flash  # 或 gemini-2.0-pro, gemini-1.5-pro 等 # 或自由输入Gemini模型

ModelScope 设置
[API_KEYS]
modelscope_api_key = your_modelscope_token_here

[MODELS]
modelscope_model = deepseek-ai/DeepSeek-R1等 # 或 自由输入模型

[MODELSCOPE]
base_url = https://api-inference.modelscope.cn/v1/

Ollama 本地模型设置
[MODELS]
ollama_model = llama3.2  # 或其他已安装的Ollama模型名称

[OLLAMA]
api_url = http://localhost:11434/api/chat
Ollama使用说明： 要使用Ollama本地模型，您需要先在本地安装Ollama，然后使用ollama pull llama3.2等命令下载模型。详细说明请参考Ollama官方文档。

SiliconFlow 模型设置
[API_KEYS]
siliconflow_api_key = your_siliconflow_api_key_here

[MODELS]
siliconflow_model = deepseek-ai/DeepSeek-R1  # 或其他支持的模型

[SILICONFLOW]
api_url = https://api.siliconflow.cn/v1/chat/completions
SiliconFlow使用说明： SiliconFlow提供了高性能的AI模型API服务，支持DeepSeek等多种模型。您需要在SiliconFlow官网注册并获取API密钥。

自定义 API 设置
[API_KEYS]
custom_openai_api_key = your_custom_api_key_here

[MODELS]
custom_openai_model = your_custom_model_name_here

[CUSTOM_OPENAI]
api_url = https://your-custom-openai-compatible-api.com/v1/chat/completions

多个自定义模型设置
[CUSTOM_OPENAI_MODELS]
models = [{
  "name": "example-model",
  "api_key": "your_custom_model_api_key_here",
  "model_name": "your_custom_model_name_here",
  "api_url": "https://your-custom-api-endpoint.com/v1/chat/completions"
}]
enabled = true


创作流程：
1. 大纲生成
在"大纲生成"标签页中填写小说的基本信息（标题、类型、主题、风格等）
设置章节数和主角数量，配角数量，反派数量，路人甲数量
选择生成范围，默认为1章，最小输入章节数为1，最大输入章节数为9999，每章字数默认为3000字，最小输入字数为200，最大输入字数为9999
选择 AI 模型并点击"生成大纲"按钮
2. 大纲编辑
在"大纲编辑"标签页中完善标题、主题、简介和世界观设定（可使用AI模型辅助生成）
在"章节大纲编辑"标签页中管理章节结构（可使用AI模型辅助生成）
使用 AI 辅助编辑功能优化大纲内容（可使用AI模型辅助生成）
3. 人物设计
在"人物编辑"标签页中创建和管理角色（可使用AI模型辅助生成）
设置角色的基本信息、背景故事、性格特点等（可使用AI模型辅助生成）
使用 AI 辅助生成丰富的角色设定（可使用AI模型辅助生成）
4. 章节创作
在"章节生成"标签页中选择要编辑的章节（可使用AI模型辅助生成）
使用 AI 辅助编辑功能生成章节内容（可使用AI模型辅助生成）
系统会自动考虑前后章节的内容，保持故事连贯性（可使用AI模型辅助生成）

5. 章节分析与润色
在"章节分析"标签页中选择要分析的章节（可使用AI模型辅助生成）
选择分析选项（如剧情分析、优缺点分析、改进建议等）（可使用AI模型辅助生成）
查看分析结果并使用"章节改进"功能根据分析结果自动润色章节内容（可使用AI模型辅助生成）

6. 保存和加载
使用工具栏上的"保存"和"打开"按钮保存和加载小说项目（.ainovel 格式）
可以导出为纯文本或其他格式。你可以选择其中一个。

---------------------分 割 线----------------------

1.  开发文档编号顺序要正确无误，不能出现断层编号。

2.  禁止创建重复相同的目录结构、文件夹及文档。

3.  提供详细的技术栈、技术线，目录及文件结构等，开发路线，不需要考虑开发时间。

4.  按顺序使用ASCII绘图，提供详细的主界面布局以及各个主要核心功能布局，首页仪表盘界面（顶部工具栏、左侧功能导航菜单、底部状态栏）->大纲生成->大纲编辑->章节编辑->章节生成->章节分析->人物编辑->人物关系图->统计信息->AI聊天->提示词库->上下文管理->向量库检索->设置。注意（大纲生成、大纲编辑、章节编辑、章节生成、章节分析、人物编辑、人物关系图均采用左右两个区域的设计，左边为功能区占用40，右边为生成区占用60）必须要全部功能的界面布局，界面布局禁止有模拟数据、假数据或测试数据。

5. Glassmorphism UI设计风格，功能内容布局要合理，颜色搭配要清新，可在网上搜索参考Glassmorphism UI UI风格，使用明亮、暗黑两套主题，（禁止使用紫色系）多颜色按钮及控件，这样用户可以更直观的知道目前的按钮状态，使用可视化颜色表，方便查看和调整以及气泡通知信息。

6.  可根据用户选择的小说类型，小说主题等来生成相对应的大纲，章节结构和剧情等，也可自定义让用户输入大纲等。（可使用AI模型辅助生成），生成符合现在的网络小说读者，如番茄小说，飞卢，17K，起点，纵横，晋江，七猫等小说群体。

7.  智能检测纠正匹配API地址后缀功能，而不是强制给API地址加上后缀，需要检测分析用户的地址是否有效，如果API地址无效导致无法成功连接，在纠正匹配后缀，对于有效的能够成功连接的API地址就无需纠正匹配后缀了。

8.  统一的API保存管理功能，方便用户快速查看查找已保存配置好的API，然后进行使用，用户只需要填写输入正确的API密钥、API地址和想使用的AI模型配置、测试连接成功后保存配置，即可全局使用。

9.  AI聊天对话功能，用于验证AI模型是否能够正常使用，也可以提问AI模型，有助于提升写作效率。

10.  向量库检索功能系统，添加嵌入模型输入配置选项。

11.  提示词库，内置多种提示词，方便用户查看及使用，还支持用户自定义提示词。内置提示词包括有：大纲相关、细钢、章节、世界观、剧情线、续写、扩写、润色、改写、优化建议、金手指生成、黄金开篇、写作风格、写作要求、人设生成、审稿、仿写、短篇等。提示词要注意中英文标点符号等，避免出现不必要的错误。 

12.  内置降AI味功能，因为有很多用AI模型生成的小说内容都有很严重的AI味，一点不像是真人写作，很机械化，而且偏离原来小说的主题。

13.  全局统一的组件、控件、按钮及SVG矢量图标，不可以使用emoji表情包。

14.  记忆窗口功能，记住用户调节窗口尺寸的大小及宽高度和应用移动的位置，避免了下次运行时还需要调节的繁琐步骤。

15.  禁止使用假数据、测试数据以及模拟数据，要确保项目应用程序在开发过程或实际使用过程都是真实数据。

16.   应用运行日记、实时日记都要纯中文显示。

17.  使用PyInstaller、Inno Setup或MSi打包为安装程序。

注意，编写全部功能界面布局时，不能出现断断续续或断层的现象，一定要按顺序编写。

允许分段编写开发文档。不需要一次性全部编写。

只需要编写开发文档、不用生成任何的代码。

