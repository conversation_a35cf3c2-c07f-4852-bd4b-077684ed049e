{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T14:20:19.481Z", "updatedAt": "2025-07-29T14:20:19.529Z", "resourceCount": 24}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-07-29T14:20:19.488Z", "updatedAt": "2025-07-29T14:20:19.488Z", "scannedAt": "2025-07-29T14:20:19.488Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-process", "source": "project", "protocol": "execution", "name": "Ai Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.490Z", "updatedAt": "2025-07-29T14:20:19.490Z", "scannedAt": "2025-07-29T14:20:19.490Z", "path": "role/ai-integration-expert/execution/ai-integration-process.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.493Z", "updatedAt": "2025-07-29T14:20:19.493Z", "scannedAt": "2025-07-29T14:20:19.493Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "ai-novel-architect", "source": "project", "protocol": "role", "name": "Ai Novel Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-architect/ai-novel-architect.role.md", "metadata": {"createdAt": "2025-07-29T14:20:19.495Z", "updatedAt": "2025-07-29T14:20:19.495Z", "scannedAt": "2025-07-29T14:20:19.495Z", "path": "role/ai-novel-architect/ai-novel-architect.role.md"}}, {"id": "ai-novel-development", "source": "project", "protocol": "execution", "name": "Ai Novel Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-architect/execution/ai-novel-development.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.498Z", "updatedAt": "2025-07-29T14:20:19.498Z", "scannedAt": "2025-07-29T14:20:19.497Z", "path": "role/ai-novel-architect/execution/ai-novel-development.execution.md"}}, {"id": "ai-novel-thinking", "source": "project", "protocol": "thought", "name": "Ai Novel Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-architect/thought/ai-novel-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.500Z", "updatedAt": "2025-07-29T14:20:19.500Z", "scannedAt": "2025-07-29T14:20:19.500Z", "path": "role/ai-novel-architect/thought/ai-novel-thinking.thought.md"}}, {"id": "ai-novel-writer", "source": "project", "protocol": "role", "name": "Ai Novel Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-writer/ai-novel-writer.role.md", "metadata": {"createdAt": "2025-07-29T14:20:19.503Z", "updatedAt": "2025-07-29T14:20:19.503Z", "scannedAt": "2025-07-29T14:20:19.503Z", "path": "role/ai-novel-writer/ai-novel-writer.role.md"}}, {"id": "creative-writing-process", "source": "project", "protocol": "execution", "name": "Creative Writing Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-writer/execution/creative-writing-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.505Z", "updatedAt": "2025-07-29T14:20:19.505Z", "scannedAt": "2025-07-29T14:20:19.505Z", "path": "role/ai-novel-writer/execution/creative-writing-process.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-writer/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.508Z", "updatedAt": "2025-07-29T14:20:19.508Z", "scannedAt": "2025-07-29T14:20:19.508Z", "path": "role/ai-novel-writer/thought/creative-writing-thinking.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-29T14:20:19.510Z", "updatedAt": "2025-07-29T14:20:19.510Z", "scannedAt": "2025-07-29T14:20:19.510Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.512Z", "updatedAt": "2025-07-29T14:20:19.512Z", "scannedAt": "2025-07-29T14:20:19.512Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.514Z", "updatedAt": "2025-07-29T14:20:19.514Z", "scannedAt": "2025-07-29T14:20:19.514Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "glassmorphism-design-process", "source": "project", "protocol": "execution", "name": "Glassmorphism Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.516Z", "updatedAt": "2025-07-29T14:20:19.516Z", "scannedAt": "2025-07-29T14:20:19.516Z", "path": "role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md"}}, {"id": "glassmorphism-designer", "source": "project", "protocol": "role", "name": "Glassmorphism Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/glassmorphism-designer/glassmorphism-designer.role.md", "metadata": {"createdAt": "2025-07-29T14:20:19.517Z", "updatedAt": "2025-07-29T14:20:19.517Z", "scannedAt": "2025-07-29T14:20:19.517Z", "path": "role/glassmorphism-designer/glassmorphism-designer.role.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.519Z", "updatedAt": "2025-07-29T14:20:19.519Z", "scannedAt": "2025-07-29T14:20:19.519Z", "path": "role/glassmorphism-designer/thought/design-thinking.thought.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "execution", "name": "Intelligent Routing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/intelligent-routing.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.520Z", "updatedAt": "2025-07-29T14:20:19.520Z", "scannedAt": "2025-07-29T14:20:19.520Z", "path": "role/system-director/execution/intelligent-routing.execution.md"}}, {"id": "project-management", "source": "project", "protocol": "execution", "name": "Project Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-management.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.521Z", "updatedAt": "2025-07-29T14:20:19.521Z", "scannedAt": "2025-07-29T14:20:19.521Z", "path": "role/system-director/execution/project-management.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.522Z", "updatedAt": "2025-07-29T14:20:19.522Z", "scannedAt": "2025-07-29T14:20:19.522Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "team-coordination", "source": "project", "protocol": "execution", "name": "Team Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/team-coordination.execution.md", "metadata": {"createdAt": "2025-07-29T14:20:19.523Z", "updatedAt": "2025-07-29T14:20:19.523Z", "scannedAt": "2025-07-29T14:20:19.523Z", "path": "role/system-director/execution/team-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-07-29T14:20:19.524Z", "updatedAt": "2025-07-29T14:20:19.524Z", "scannedAt": "2025-07-29T14:20:19.524Z", "path": "role/system-director/system-director.role.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "thought", "name": "Intelligent Routing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/intelligent-routing.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.525Z", "updatedAt": "2025-07-29T14:20:19.525Z", "scannedAt": "2025-07-29T14:20:19.525Z", "path": "role/system-director/thought/intelligent-routing.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.526Z", "updatedAt": "2025-07-29T14:20:19.526Z", "scannedAt": "2025-07-29T14:20:19.526Z", "path": "role/system-director/thought/quality-control.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.526Z", "updatedAt": "2025-07-29T14:20:19.526Z", "scannedAt": "2025-07-29T14:20:19.526Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "team-coordination", "source": "project", "protocol": "thought", "name": "Team Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/team-coordination.thought.md", "metadata": {"createdAt": "2025-07-29T14:20:19.527Z", "updatedAt": "2025-07-29T14:20:19.527Z", "scannedAt": "2025-07-29T14:20:19.527Z", "path": "role/system-director/thought/team-coordination.thought.md"}}], "stats": {"totalResources": 24, "byProtocol": {"role": 6, "execution": 9, "thought": 9}, "bySource": {"project": 24}}}